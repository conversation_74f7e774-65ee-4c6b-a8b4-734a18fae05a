/mnt/d/Epoch of Elria/Epoch-of-Elria/target/release/deps/libwayland_protocols-5af948921a6f8b5d.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/protocol_macro.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/unstable.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/misc.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/wlr.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/stable.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/fullscreen-shell-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/idle-inhibit-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/input-method-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/input-timestamps-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/linux-dmabuf-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/linux-explicit-synchronization-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/pointer-constraints-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/pointer-gestures-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/primary-selection-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/relative-pointer-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/tablet-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/tablet-v2_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/text-input-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/text-input-v3_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-decoration-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-foreign-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-foreign-v2_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-output-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-shell-v5_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-shell-v6_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/gtk-primary-selection_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-data-control-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-export-dmabuf-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-gamma-control-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-input-inhibitor-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-layer-shell-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-output-management-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-output-power-management-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-screencopy-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-virtual-pointer-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/presentation-time_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-shell_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/viewporter_client_api.rs

/mnt/d/Epoch of Elria/Epoch-of-Elria/target/release/deps/libwayland_protocols-5af948921a6f8b5d.rlib: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/protocol_macro.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/unstable.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/misc.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/wlr.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/stable.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/fullscreen-shell-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/idle-inhibit-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/input-method-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/input-timestamps-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/linux-dmabuf-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/linux-explicit-synchronization-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/pointer-constraints-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/pointer-gestures-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/primary-selection-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/relative-pointer-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/tablet-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/tablet-v2_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/text-input-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/text-input-v3_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-decoration-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-foreign-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-foreign-v2_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-output-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-shell-v5_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-shell-v6_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/gtk-primary-selection_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-data-control-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-export-dmabuf-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-gamma-control-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-input-inhibitor-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-layer-shell-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-output-management-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-output-power-management-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-screencopy-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-virtual-pointer-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/presentation-time_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-shell_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/viewporter_client_api.rs

/mnt/d/Epoch of Elria/Epoch-of-Elria/target/release/deps/wayland_protocols-5af948921a6f8b5d.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/protocol_macro.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/unstable.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/misc.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/wlr.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/stable.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/fullscreen-shell-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/idle-inhibit-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/input-method-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/input-timestamps-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/linux-dmabuf-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/linux-explicit-synchronization-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/pointer-constraints-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/pointer-gestures-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/primary-selection-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/relative-pointer-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/tablet-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/tablet-v2_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/text-input-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/text-input-v3_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-decoration-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-foreign-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-foreign-v2_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-output-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-shell-v5_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-shell-v6_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/gtk-primary-selection_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-data-control-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-export-dmabuf-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-gamma-control-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-input-inhibitor-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-layer-shell-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-output-management-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-output-power-management-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-screencopy-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-virtual-pointer-v1_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/presentation-time_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-shell_client_api.rs /mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/viewporter_client_api.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/lib.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/protocol_macro.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/unstable.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/misc.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/wlr.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/stable.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/fullscreen-shell-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/idle-inhibit-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/input-method-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/input-timestamps-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/keyboard-shortcuts-inhibit-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/linux-dmabuf-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/linux-explicit-synchronization-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/pointer-constraints-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/pointer-gestures-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/primary-selection-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/relative-pointer-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/tablet-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/tablet-v2_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/text-input-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/text-input-v3_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-decoration-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-foreign-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-foreign-v2_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-output-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-shell-v5_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-shell-v6_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xwayland-keyboard-grab-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/gtk-primary-selection_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-data-control-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-export-dmabuf-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-foreign-toplevel-management-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-gamma-control-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-input-inhibitor-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-layer-shell-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-output-management-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-output-power-management-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-screencopy-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/wlr-virtual-pointer-v1_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/presentation-time_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/xdg-shell_client_api.rs:
/mnt/d/Epoch\ of\ Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out/viewporter_client_api.rs:

# env-dep:OUT_DIR=/mnt/d/Epoch of Elria/Epoch-of-Elria/target/release/build/wayland-protocols-5d0143a9893ddf9e/out
