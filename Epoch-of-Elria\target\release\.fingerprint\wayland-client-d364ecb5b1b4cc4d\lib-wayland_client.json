{"rustc": 15597765236515928571, "features": "[\"dlopen\", \"scoped-tls\", \"use_system_lib\"]", "declared_features": "[\"dlopen\", \"scoped-tls\", \"use_system_lib\"]", "target": 6885752962907571681, "profile": 2040997289075261528, "path": 11516442890180196779, "deps": [[735493956294706931, "nix", false, 10402276961977460299], [2924422107542798392, "libc", false, 11562270898679172843], [8289600954469699483, "build_script_build", false, 1530491881773162664], [10435729446543529114, "bitflags", false, 5030592503563974220], [11040873101195677033, "wayland_sys", false, 16825460564198339296], [11434239582363224126, "downcast_rs", false, 15105812377715924533], [11616214658452515874, "wayland_commons", false, 9197144979527494613], [13370890382188185363, "scoped_tls", false, 9306207434042712251]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wayland-client-d364ecb5b1b4cc4d/dep-lib-wayland_client", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}