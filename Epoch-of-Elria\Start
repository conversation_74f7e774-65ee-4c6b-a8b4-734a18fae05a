#!/bin/bash

echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              EPOCH OF ELRIA GAME ENGINE                     ║"
echo "║                   Application Launcher                      ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""
echo "🌍 Starting Epoch of Elria Game Engine..."

# Build the game engine if it doesn't exist
if [ ! -f "EpochOfElriaEngine" ]; then
    echo "🔨 Building game engine..."
    g++ -std=c++17 -O2 standalone_game_engine.cpp -o EpochOfElriaEngine

    if [ $? -ne 0 ]; then
        echo "❌ Build failed! Trying existing games..."
        if [ -f "standalone_game_engine" ]; then
            echo "🚀 Using existing standalone engine..."
            cp standalone_game_engine EpochOfElriaEngine
        elif [ -f "simple_3d_game" ]; then
            echo "🚀 Using simple 3D game..."
            cp simple_3d_game EpochOfElriaEngine
        else
            echo "❌ No games available! Please install g++ compiler."
            exit 1
        fi
    else
        echo "✅ Build successful!"
    fi
fi

echo "🚀 Launching Epoch of Elria Game Engine..."
echo ""

# Try to open in a new terminal window (application-like behavior)
if command -v gnome-terminal >/dev/null 2>&1; then
    echo "🎮 Opening in new application window..."
    gnome-terminal --title="🌍 Epoch of Elria Game Engine" --geometry=120x40 -- bash -c "./EpochOfElriaEngine; echo ''; echo 'Press any key to close...'; read -n 1"
elif command -v xterm >/dev/null 2>&1; then
    echo "🎮 Opening in new application window..."
    xterm -title "🌍 Epoch of Elria Game Engine" -geometry 120x40 -e bash -c "./EpochOfElriaEngine; echo ''; echo 'Press any key to close...'; read -n 1" &
elif command -v konsole >/dev/null 2>&1; then
    echo "🎮 Opening in new application window..."
    konsole --title "🌍 Epoch of Elria Game Engine" -e bash -c "./EpochOfElriaEngine; echo ''; echo 'Press any key to close...'; read -n 1" &
else
    echo "🎮 Starting in current terminal..."
    echo "Note: Install gnome-terminal, xterm, or konsole for windowed mode"
    echo ""
    ./EpochOfElriaEngine
fi

echo "✨ Game engine launched!"
echo ""
echo "If a new window opened, check your screen for the game!"
echo "Controls: W/A/S/D - Move Camera | E - Edit Mode | X - Create Platform | Q - Quit"
