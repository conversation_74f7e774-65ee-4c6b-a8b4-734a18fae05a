#!/bin/bash

echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              EPOCH OF ELRIA GAME ENGINE                     ║"
echo "║                   Unified Application                       ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""
echo "🌍 Starting Epoch of Elria 3D Game Engine..."

# Check if windowed engine exists, if not build it
if [ ! -f "EpochOfElriaEngine" ]; then
    echo "🔨 Building windowed 3D application..."

    # Try to build with OpenGL (Linux)
    g++ -std=c++17 -O2 windowed_app_engine.cpp -o EpochOfElriaEngine -lGL -lGLU -lX11 2>/dev/null

    if [ $? -ne 0 ]; then
        echo "❌ OpenGL build failed! Trying fallback windowed version..."
        g++ -std=c++17 -O2 windowed_game_engine.cpp -o windowed_game_engine

        if [ $? -ne 0 ]; then
            echo "❌ Windowed build failed! Trying standalone version..."
            if [ ! -f "standalone_game_engine" ]; then
                g++ -std=c++17 -O2 standalone_game_engine.cpp -o standalone_game_engine
            fi

            if [ -f "standalone_game_engine" ]; then
                echo "🚀 Starting standalone engine..."
                ./standalone_game_engine
            else
                echo "❌ Could not build engine. Trying existing games..."
                if [ -f "simple_3d_game" ]; then
                    ./simple_3d_game
                elif [ -f "game_svg" ]; then
                    ./game_svg
                else
                    echo "❌ No games available. Please install g++ compiler."
                    echo ""
                    echo "Required: g++ with OpenGL development libraries"
                    echo "Install: sudo apt install g++ libgl1-mesa-dev libglu1-mesa-dev libx11-dev"
                fi
            fi
            exit 1
        else
            echo "✅ Fallback windowed version built successfully!"
            echo "🚀 Launching windowed game engine..."
            ./windowed_game_engine
            exit 0
        fi
    else
        echo "✅ 3D windowed application built successfully!"
    fi
fi

echo "🚀 Launching Epoch of Elria 3D Game Engine..."
echo "🎮 Opening application window..."
./EpochOfElriaEngine
