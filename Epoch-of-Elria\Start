#!/bin/bash

echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              EPOCH OF ELRIA GAME ENGINE                     ║"
echo "║                   Windowed Application                      ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""
echo "🌍 Starting Epoch of Elria 3D Game Engine..."

# Build the windowed HTML5 version
echo "🔨 Building windowed application..."
g++ -std=c++17 -O2 wsl_windowed_engine.cpp -o WindowedEngine

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "🚀 Generating windowed application..."
    ./WindowedEngine
else
    echo "❌ Build failed! Using fallback method..."

    # Fallback: Build standalone and run in new terminal
    if [ ! -f "EpochOfElriaEngine" ]; then
        echo "🔨 Building standalone engine..."
        g++ -std=c++17 -O2 standalone_game_engine.cpp -o EpochOfElriaEngine

        if [ $? -ne 0 ]; then
            echo "❌ Build failed! Trying existing games..."
            if [ -f "standalone_game_engine" ]; then
                cp standalone_game_engine EpochOfElriaEngine
            elif [ -f "simple_3d_game" ]; then
                cp simple_3d_game EpochOfElriaEngine
            else
                echo "❌ No games available! Please install g++ compiler."
                exit 1
            fi
        fi
    fi

    echo "🎮 Opening in new terminal window..."
    # Try to open in a new terminal window
    if command -v gnome-terminal >/dev/null 2>&1; then
        gnome-terminal --title="🌍 Epoch of Elria Game Engine" --geometry=120x40 -- bash -c "./EpochOfElriaEngine; echo ''; echo 'Press any key to close...'; read -n 1"
    elif command -v xterm >/dev/null 2>&1; then
        xterm -title "🌍 Epoch of Elria Game Engine" -geometry 120x40 -e bash -c "./EpochOfElriaEngine; echo ''; echo 'Press any key to close...'; read -n 1" &
    else
        echo "🎮 Starting in current terminal..."
        ./EpochOfElriaEngine
    fi
fi
