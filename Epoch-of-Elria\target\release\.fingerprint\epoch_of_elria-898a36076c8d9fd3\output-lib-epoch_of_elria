{"$message_type":"diagnostic","message":"cannot find type `AdvancedRenderingSystem` in this scope","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"src/advanced_rendering.rs","byte_start":25942,"byte_end":25965,"line_start":855,"line_end":855,"column_start":6,"column_end":29,"is_primary":true,"text":[{"text":"impl AdvancedRenderingSystem {","highlight_start":6,"highlight_end":29}],"label":"not found in this scope","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m: cannot find type `AdvancedRenderingSystem` in this scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/advanced_rendering.rs:855:6\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m855\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl AdvancedRenderingSystem {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in this scope\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Collectible`, `Enemy`, `Platform`, and `Player`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/game_framework.rs","byte_start":123,"byte_end":129,"line_start":3,"line_end":3,"column_start":39,"column_end":45,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":39,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/game_framework.rs","byte_start":131,"byte_end":142,"line_start":3,"line_end":3,"column_start":47,"column_end":58,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":47,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/game_framework.rs","byte_start":144,"byte_end":149,"line_start":3,"line_end":3,"column_start":60,"column_end":65,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":60,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/game_framework.rs","byte_start":151,"byte_end":159,"line_start":3,"line_end":3,"column_start":67,"column_end":75,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":67,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/game_framework.rs","byte_start":121,"byte_end":159,"line_start":3,"line_end":3,"column_start":37,"column_end":75,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":37,"highlight_end":75}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/game_framework.rs","byte_start":110,"byte_end":111,"line_start":3,"line_end":3,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/game_framework.rs","byte_start":159,"byte_end":160,"line_start":3,"line_end":3,"column_start":75,"column_end":76,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":75,"highlight_end":76}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Collectible`, `Enemy`, `Platform`, and `Player`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_framework.rs:3:39\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::scene::Scene`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/game_framework.rs","byte_start":166,"byte_end":185,"line_start":4,"line_end":4,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use crate::scene::Scene;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/game_framework.rs","byte_start":162,"byte_end":187,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::scene::Scene;","highlight_start":1,"highlight_end":25},{"text":"use crate::input::{InputManager, Key};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::scene::Scene`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_framework.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::scene::Scene;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `InputManager` and `Key`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/game_framework.rs","byte_start":206,"byte_end":218,"line_start":5,"line_end":5,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"use crate::input::{InputManager, Key};","highlight_start":20,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/game_framework.rs","byte_start":220,"byte_end":223,"line_start":5,"line_end":5,"column_start":34,"column_end":37,"is_primary":true,"text":[{"text":"use crate::input::{InputManager, Key};","highlight_start":34,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/game_framework.rs","byte_start":187,"byte_end":226,"line_start":5,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::input::{InputManager, Key};","highlight_start":1,"highlight_end":39},{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `InputManager` and `Key`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_framework.rs:5:20\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::input::{InputManager, Key};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Isometry3`, `Perspective3`, `Point3`, `Translation3`, `UnitQuaternion`, and `Vector3`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/advanced_rendering.rs","byte_start":108,"byte_end":114,"line_start":3,"line_end":3,"column_start":24,"column_end":30,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":24,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/advanced_rendering.rs","byte_start":116,"byte_end":123,"line_start":3,"line_end":3,"column_start":32,"column_end":39,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":32,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/advanced_rendering.rs","byte_start":134,"byte_end":146,"line_start":3,"line_end":3,"column_start":50,"column_end":62,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":50,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/advanced_rendering.rs","byte_start":148,"byte_end":157,"line_start":3,"line_end":3,"column_start":64,"column_end":73,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":64,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/advanced_rendering.rs","byte_start":159,"byte_end":171,"line_start":3,"line_end":3,"column_start":75,"column_end":87,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":75,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/advanced_rendering.rs","byte_start":173,"byte_end":187,"line_start":3,"line_end":3,"column_start":89,"column_end":103,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":89,"highlight_end":103}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/advanced_rendering.rs","byte_start":108,"byte_end":125,"line_start":3,"line_end":3,"column_start":24,"column_end":41,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":24,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/advanced_rendering.rs","byte_start":132,"byte_end":187,"line_start":3,"line_end":3,"column_start":48,"column_end":103,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":48,"highlight_end":103}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/advanced_rendering.rs","byte_start":107,"byte_end":108,"line_start":3,"line_end":3,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/advanced_rendering.rs","byte_start":187,"byte_end":188,"line_start":3,"line_end":3,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Isometry3`, `Perspective3`, `Point3`, `Translation3`, `UnitQuaternion`, and `Vector3`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/advanced_rendering.rs:3:24\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `kiss3d::scene::SceneNode`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/advanced_rendering.rs","byte_start":194,"byte_end":218,"line_start":4,"line_end":4,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"use kiss3d::scene::SceneNode;","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/advanced_rendering.rs","byte_start":190,"byte_end":220,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use kiss3d::scene::SceneNode;","highlight_start":1,"highlight_end":30},{"text":"use kiss3d::window::Window;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `kiss3d::scene::SceneNode`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/advanced_rendering.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kiss3d::scene::SceneNode;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `kiss3d::light::Light`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/advanced_rendering.rs","byte_start":252,"byte_end":272,"line_start":6,"line_end":6,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"use kiss3d::light::Light;","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/advanced_rendering.rs","byte_start":248,"byte_end":274,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use kiss3d::light::Light;","highlight_start":1,"highlight_end":26},{"text":"use kiss3d::camera::{Camera, ArcBall, FirstPerson};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `kiss3d::light::Light`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/advanced_rendering.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kiss3d::light::Light;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ArcBall`, `Camera`, and `FirstPerson`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/advanced_rendering.rs","byte_start":295,"byte_end":301,"line_start":7,"line_end":7,"column_start":22,"column_end":28,"is_primary":true,"text":[{"text":"use kiss3d::camera::{Camera, ArcBall, FirstPerson};","highlight_start":22,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/advanced_rendering.rs","byte_start":303,"byte_end":310,"line_start":7,"line_end":7,"column_start":30,"column_end":37,"is_primary":true,"text":[{"text":"use kiss3d::camera::{Camera, ArcBall, FirstPerson};","highlight_start":30,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/advanced_rendering.rs","byte_start":312,"byte_end":323,"line_start":7,"line_end":7,"column_start":39,"column_end":50,"is_primary":true,"text":[{"text":"use kiss3d::camera::{Camera, ArcBall, FirstPerson};","highlight_start":39,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/advanced_rendering.rs","byte_start":274,"byte_end":326,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use kiss3d::camera::{Camera, ArcBall, FirstPerson};","highlight_start":1,"highlight_end":52},{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ArcBall`, `Camera`, and `FirstPerson`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/advanced_rendering.rs:7:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kiss3d::camera::{Camera, ArcBall, FirstPerson};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Collectible`, `Enemy`, `GameObject`, `Platform`, and `Player`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/game_templates.rs","byte_start":138,"byte_end":148,"line_start":4,"line_end":4,"column_start":27,"column_end":37,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};","highlight_start":27,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/game_templates.rs","byte_start":150,"byte_end":156,"line_start":4,"line_end":4,"column_start":39,"column_end":45,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};","highlight_start":39,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/game_templates.rs","byte_start":158,"byte_end":163,"line_start":4,"line_end":4,"column_start":47,"column_end":52,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};","highlight_start":47,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/game_templates.rs","byte_start":165,"byte_end":176,"line_start":4,"line_end":4,"column_start":54,"column_end":65,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};","highlight_start":54,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/game_templates.rs","byte_start":178,"byte_end":186,"line_start":4,"line_end":4,"column_start":67,"column_end":75,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};","highlight_start":67,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/game_templates.rs","byte_start":112,"byte_end":189,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};","highlight_start":1,"highlight_end":77},{"text":"use crate::scene::Scene;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Collectible`, `Enemy`, `GameObject`, `Platform`, and `Player`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_templates.rs:4:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::advanced_rendering::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/game_templates.rs","byte_start":257,"byte_end":285,"line_start":7,"line_end":7,"column_start":5,"column_end":33,"is_primary":true,"text":[{"text":"use crate::advanced_rendering::*;","highlight_start":5,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/game_templates.rs","byte_start":253,"byte_end":287,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::advanced_rendering::*;","highlight_start":1,"highlight_end":34},{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::advanced_rendering::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_templates.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::advanced_rendering::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `update_water` found for mutable reference `&mut AdvancedRenderer` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/advanced_rendering.rs","byte_start":19341,"byte_end":19353,"line_start":678,"line_end":678,"column_start":14,"column_end":26,"is_primary":true,"text":[{"text":"        self.update_water(delta_time);","highlight_start":14,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `update` with a similar name","code":null,"level":"help","spans":[{"file_name":"src/advanced_rendering.rs","byte_start":19341,"byte_end":19353,"line_start":678,"line_end":678,"column_start":14,"column_end":26,"is_primary":true,"text":[{"text":"        self.update_water(delta_time);","highlight_start":14,"highlight_end":26}],"label":null,"suggested_replacement":"update","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `update_water` found for mutable reference `&mut AdvancedRenderer` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/advanced_rendering.rs:678:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m678\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.update_water(delta_time);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `update` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m678\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        self.\u001b[0m\u001b[0m\u001b[38;5;9mupdate_water\u001b[0m\u001b[0m(delta_time);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m678\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        self.\u001b[0m\u001b[0m\u001b[38;5;10mupdate\u001b[0m\u001b[0m(delta_time);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `GameObject`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/physics.rs","byte_start":105,"byte_end":115,"line_start":4,"line_end":4,"column_start":27,"column_end":37,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, BoundingBox};","highlight_start":27,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `GameObject`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/physics.rs:4:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::game_objects::{GameObject, BoundingBox};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `GameObject`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/game_framework.rs","byte_start":111,"byte_end":121,"line_start":3,"line_end":3,"column_start":27,"column_end":37,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":27,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `GameObject`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_framework.rs:3:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `delta_time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/game_objects.rs","byte_start":3598,"byte_end":3608,"line_start":132,"line_end":132,"column_start":51,"column_end":61,"is_primary":true,"text":[{"text":"    pub fn apply_gravity(&mut self, gravity: f32, delta_time: f32) {","highlight_start":51,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/game_objects.rs","byte_start":3598,"byte_end":3608,"line_start":132,"line_end":132,"column_start":51,"column_end":61,"is_primary":true,"text":[{"text":"    pub fn apply_gravity(&mut self, gravity: f32, delta_time: f32) {","highlight_start":51,"highlight_end":61}],"label":null,"suggested_replacement":"_delta_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `delta_time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_objects.rs:132:51\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn apply_gravity(&mut self, gravity: f32, delta_time: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_delta_time`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `current_time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/game_objects.rs","byte_start":13381,"byte_end":13393,"line_start":481,"line_end":481,"column_start":60,"column_end":72,"is_primary":true,"text":[{"text":"    pub fn update_ai(&mut self, player_position: Vector3D, current_time: f32) {","highlight_start":60,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/game_objects.rs","byte_start":13381,"byte_end":13393,"line_start":481,"line_end":481,"column_start":60,"column_end":72,"is_primary":true,"text":[{"text":"    pub fn update_ai(&mut self, player_position: Vector3D, current_time: f32) {","highlight_start":60,"highlight_end":72}],"label":null,"suggested_replacement":"_current_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `current_time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_objects.rs:481:60\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m481\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn update_ai(&mut self, player_position: Vector3D, current_time: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_current_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `scene`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/rendering.rs","byte_start":1952,"byte_end":1957,"line_start":66,"line_end":66,"column_start":30,"column_end":35,"is_primary":true,"text":[{"text":"    pub fn render(&mut self, scene: &Scene) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":30,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/rendering.rs","byte_start":1952,"byte_end":1957,"line_start":66,"line_end":66,"column_start":30,"column_end":35,"is_primary":true,"text":[{"text":"    pub fn render(&mut self, scene: &Scene) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":30,"highlight_end":35}],"label":null,"suggested_replacement":"_scene","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `scene`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/rendering.rs:66:30\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn render(&mut self, scene: &Scene) -> Result<(), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_scene`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `title`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/rendering.rs","byte_start":3930,"byte_end":3935,"line_start":131,"line_end":131,"column_start":40,"column_end":45,"is_primary":true,"text":[{"text":"    pub fn set_window_title(&mut self, title: &str) {","highlight_start":40,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/rendering.rs","byte_start":3930,"byte_end":3935,"line_start":131,"line_end":131,"column_start":40,"column_end":45,"is_primary":true,"text":[{"text":"    pub fn set_window_title(&mut self, title: &str) {","highlight_start":40,"highlight_end":45}],"label":null,"suggested_replacement":"_title","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `title`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/rendering.rs:131:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_window_title(&mut self, title: &str) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_title`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `camera`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/rendering.rs","byte_start":4736,"byte_end":4742,"line_start":148,"line_end":148,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.arcball_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/rendering.rs","byte_start":4736,"byte_end":4742,"line_start":148,"line_end":148,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.arcball_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":"_camera","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `camera`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/rendering.rs:148:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if let Some(ref mut camera) = self.arcball_camera {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_camera`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `camera`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/rendering.rs","byte_start":4935,"byte_end":4941,"line_start":153,"line_end":153,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.fps_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/rendering.rs","byte_start":4935,"byte_end":4941,"line_start":153,"line_end":153,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.fps_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":"_camera","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `camera`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/rendering.rs:153:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m153\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if let Some(ref mut camera) = self.fps_camera {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_camera`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `kiss3d_direction`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/rendering.rs","byte_start":4987,"byte_end":5003,"line_start":154,"line_end":154,"column_start":25,"column_end":41,"is_primary":true,"text":[{"text":"                    let kiss3d_direction = Vector3::new(direction.x, direction.y, direction.z);","highlight_start":25,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/rendering.rs","byte_start":4987,"byte_end":5003,"line_start":154,"line_end":154,"column_start":25,"column_end":41,"is_primary":true,"text":[{"text":"                    let kiss3d_direction = Vector3::new(direction.x, direction.y, direction.z);","highlight_start":25,"highlight_end":41}],"label":null,"suggested_replacement":"_kiss3d_direction","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `kiss3d_direction`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/rendering.rs:154:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m154\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let kiss3d_direction = Vector3::new(direction.x, direction.y, direction.z);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_kiss3d_direction`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `amount`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/rendering.rs","byte_start":4549,"byte_end":4555,"line_start":144,"line_end":144,"column_start":56,"column_end":62,"is_primary":true,"text":[{"text":"    pub fn move_camera(&mut self, direction: Vector3D, amount: f32) {","highlight_start":56,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/rendering.rs","byte_start":4549,"byte_end":4555,"line_start":144,"line_end":144,"column_start":56,"column_end":62,"is_primary":true,"text":[{"text":"    pub fn move_camera(&mut self, direction: Vector3D, amount: f32) {","highlight_start":56,"highlight_end":62}],"label":null,"suggested_replacement":"_amount","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `amount`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/rendering.rs:144:56\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m144\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn move_camera(&mut self, direction: Vector3D, amount: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_amount`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `camera`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/rendering.rs","byte_start":5469,"byte_end":5475,"line_start":167,"line_end":167,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.fps_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/rendering.rs","byte_start":5469,"byte_end":5475,"line_start":167,"line_end":167,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.fps_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":"_camera","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `camera`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/rendering.rs:167:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m167\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if let Some(ref mut camera) = self.fps_camera {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_camera`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `yaw`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/rendering.rs","byte_start":5207,"byte_end":5210,"line_start":161,"line_end":161,"column_start":37,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn rotate_camera(&mut self, yaw: f32, pitch: f32) {","highlight_start":37,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/rendering.rs","byte_start":5207,"byte_end":5210,"line_start":161,"line_end":161,"column_start":37,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn rotate_camera(&mut self, yaw: f32, pitch: f32) {","highlight_start":37,"highlight_end":40}],"label":null,"suggested_replacement":"_yaw","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `yaw`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/rendering.rs:161:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn rotate_camera(&mut self, yaw: f32, pitch: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_yaw`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `pitch`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/rendering.rs","byte_start":5217,"byte_end":5222,"line_start":161,"line_end":161,"column_start":47,"column_end":52,"is_primary":true,"text":[{"text":"    pub fn rotate_camera(&mut self, yaw: f32, pitch: f32) {","highlight_start":47,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/rendering.rs","byte_start":5217,"byte_end":5222,"line_start":161,"line_end":161,"column_start":47,"column_end":52,"is_primary":true,"text":[{"text":"    pub fn rotate_camera(&mut self, yaw: f32, pitch: f32) {","highlight_start":47,"highlight_end":52}],"label":null,"suggested_replacement":"_pitch","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `pitch`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/rendering.rs:161:47\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn rotate_camera(&mut self, yaw: f32, pitch: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_pitch`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `camera`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/rendering.rs","byte_start":5764,"byte_end":5770,"line_start":177,"line_end":177,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.arcball_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/rendering.rs","byte_start":5764,"byte_end":5770,"line_start":177,"line_end":177,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.arcball_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":"_camera","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `camera`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/rendering.rs:177:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if let Some(ref mut camera) = self.arcball_camera {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_camera`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `amount`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/rendering.rs","byte_start":5643,"byte_end":5649,"line_start":174,"line_end":174,"column_start":35,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn zoom_camera(&mut self, amount: f32) {","highlight_start":35,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/rendering.rs","byte_start":5643,"byte_end":5649,"line_start":174,"line_end":174,"column_start":35,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn zoom_camera(&mut self, amount: f32) {","highlight_start":35,"highlight_end":41}],"label":null,"suggested_replacement":"_amount","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `amount`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/rendering.rs:174:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m174\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn zoom_camera(&mut self, amount: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_amount`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unreachable pattern","code":{"code":"unreachable_patterns","explanation":null},"level":"warning","spans":[{"file_name":"src/input.rs","byte_start":6209,"byte_end":6210,"line_start":168,"line_end":168,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"                        _ => {},","highlight_start":25,"highlight_end":26}],"label":"no value can reach this","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"multiple earlier patterns match some of the same values","code":null,"level":"note","spans":[{"file_name":"src/input.rs","byte_start":4513,"byte_end":4541,"line_start":140,"line_end":140,"column_start":25,"column_end":53,"is_primary":false,"text":[{"text":"                        kiss3d::event::Action::Press => {","highlight_start":25,"highlight_end":53}],"label":"matches some of the same values","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/input.rs","byte_start":5358,"byte_end":5388,"line_start":154,"line_end":154,"column_start":25,"column_end":55,"is_primary":false,"text":[{"text":"                        kiss3d::event::Action::Release => {","highlight_start":25,"highlight_end":55}],"label":"matches some of the same values","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/input.rs","byte_start":6209,"byte_end":6210,"line_start":168,"line_end":168,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"                        _ => {},","highlight_start":25,"highlight_end":26}],"label":"collectively making this unreachable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"`#[warn(unreachable_patterns)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unreachable pattern\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/input.rs:168:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        _ => {},\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mno value can reach this\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: multiple earlier patterns match some of the same values\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/input.rs:168:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m140\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        kiss3d::event::Action::Press => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmatches some of the same values\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m154\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        kiss3d::event::Action::Release => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmatches some of the same values\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        _ => {},\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mcollectively making this unreachable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unreachable_patterns)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `dt`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/physics.rs","byte_start":2450,"byte_end":2452,"line_start":93,"line_end":93,"column_start":32,"column_end":34,"is_primary":true,"text":[{"text":"    fn apply_forces(&mut self, dt: f32) {","highlight_start":32,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/physics.rs","byte_start":2450,"byte_end":2452,"line_start":93,"line_end":93,"column_start":32,"column_end":34,"is_primary":true,"text":[{"text":"    fn apply_forces(&mut self, dt: f32) {","highlight_start":32,"highlight_end":34}],"label":null,"suggested_replacement":"_dt","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `dt`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/physics.rs:93:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m93\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn apply_forces(&mut self, dt: f32) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_dt`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `body1`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/physics.rs","byte_start":6611,"byte_end":6616,"line_start":205,"line_end":205,"column_start":68,"column_end":73,"is_primary":true,"text":[{"text":"    fn handle_trigger_collision(&mut self, id1: usize, id2: usize, body1: &RigidBody, body2: &RigidBody) {","highlight_start":68,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/physics.rs","byte_start":6611,"byte_end":6616,"line_start":205,"line_end":205,"column_start":68,"column_end":73,"is_primary":true,"text":[{"text":"    fn handle_trigger_collision(&mut self, id1: usize, id2: usize, body1: &RigidBody, body2: &RigidBody) {","highlight_start":68,"highlight_end":73}],"label":null,"suggested_replacement":"_body1","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `body1`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/physics.rs:205:68\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn handle_trigger_collision(&mut self, id1: usize, id2: usize, body1: &RigidBody, body2: &RigidBody) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_body1`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `body2`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/physics.rs","byte_start":6630,"byte_end":6635,"line_start":205,"line_end":205,"column_start":87,"column_end":92,"is_primary":true,"text":[{"text":"    fn handle_trigger_collision(&mut self, id1: usize, id2: usize, body1: &RigidBody, body2: &RigidBody) {","highlight_start":87,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/physics.rs","byte_start":6630,"byte_end":6635,"line_start":205,"line_end":205,"column_start":87,"column_end":92,"is_primary":true,"text":[{"text":"    fn handle_trigger_collision(&mut self, id1: usize, id2: usize, body1: &RigidBody, body2: &RigidBody) {","highlight_start":87,"highlight_end":92}],"label":null,"suggested_replacement":"_body2","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `body2`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/physics.rs:205:87\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn handle_trigger_collision(&mut self, id1: usize, id2: usize, body1: &RigidBody, body2: &RigidBody) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_body2`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `current`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/audio.rs","byte_start":3895,"byte_end":3902,"line_start":124,"line_end":124,"column_start":21,"column_end":28,"is_primary":true,"text":[{"text":"        if let Some(current) = &self.current_music {","highlight_start":21,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/audio.rs","byte_start":3895,"byte_end":3902,"line_start":124,"line_end":124,"column_start":21,"column_end":28,"is_primary":true,"text":[{"text":"        if let Some(current) = &self.current_music {","highlight_start":21,"highlight_end":28}],"label":null,"suggested_replacement":"_current","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `current`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/audio.rs:124:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(current) = &self.current_music {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_current`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `delta_time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/game_framework.rs","byte_start":12312,"byte_end":12322,"line_start":455,"line_end":455,"column_start":30,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn update(&mut self, delta_time: f32) {","highlight_start":30,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/game_framework.rs","byte_start":12312,"byte_end":12322,"line_start":455,"line_end":455,"column_start":30,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn update(&mut self, delta_time: f32) {","highlight_start":30,"highlight_end":40}],"label":null,"suggested_replacement":"_delta_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `delta_time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_framework.rs:455:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m455\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn update(&mut self, delta_time: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_delta_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as immutable because it is also borrowed as mutable","code":{"code":"E0502","explanation":"A variable already borrowed with a certain mutability (either mutable or\nimmutable) was borrowed again with a different mutability.\n\nErroneous code example:\n\n```compile_fail,E0502\nfn bar(x: &mut i32) {}\nfn foo(a: &mut i32) {\n    let y = &a; // a is borrowed as immutable.\n    bar(a); // error: cannot borrow `*a` as mutable because `a` is also borrowed\n            //        as immutable\n    println!(\"{}\", y);\n}\n```\n\nTo fix this error, ensure that you don't have any other references to the\nvariable before trying to access it with a different mutability:\n\n```\nfn bar(x: &mut i32) {}\nfn foo(a: &mut i32) {\n    bar(a);\n    let y = &a; // ok!\n    println!(\"{}\", y);\n}\n```\n\nFor more information on Rust's ownership system, take a look at the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n"},"level":"error","spans":[{"file_name":"src/advanced_rendering.rs","byte_start":19995,"byte_end":19999,"line_start":694,"line_end":694,"column_start":36,"column_end":40,"is_primary":true,"text":[{"text":"                    let particle = self.create_particle(&emitter);","highlight_start":36,"highlight_end":40}],"label":"immutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/advanced_rendering.rs","byte_start":19471,"byte_end":19497,"line_start":682,"line_end":682,"column_start":37,"column_end":63,"is_primary":false,"text":[{"text":"        for (_, particle_system) in &mut self.particle_systems {","highlight_start":37,"highlight_end":63}],"label":"mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/advanced_rendering.rs","byte_start":19471,"byte_end":19497,"line_start":682,"line_end":682,"column_start":37,"column_end":63,"is_primary":false,"text":[{"text":"        for (_, particle_system) in &mut self.particle_systems {","highlight_start":37,"highlight_end":63}],"label":"mutable borrow later used here","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/advanced_rendering.rs","byte_start":19471,"byte_end":19497,"line_start":682,"line_end":682,"column_start":37,"column_end":63,"is_primary":false,"text":[{"text":"        for (_, particle_system) in &mut self.particle_systems {","highlight_start":37,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `for` loop","def_site_span":{"file_name":"src/lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0502]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as immutable because it is also borrowed as mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/advanced_rendering.rs:694:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m682\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        for (_, particle_system) in &mut self.particle_systems {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmutable borrow occurs here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmutable borrow later used here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m694\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let particle = self.create_particle(&emitter);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mimmutable borrow occurs here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `player_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/game_templates.rs","byte_start":14214,"byte_end":14223,"line_start":512,"line_end":512,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"        let player_id = scene.add_player(self.config.player_start_position);","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/game_templates.rs","byte_start":14214,"byte_end":14223,"line_start":512,"line_end":512,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"        let player_id = scene.add_player(self.config.player_start_position);","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":"_player_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `player_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_templates.rs:512:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m512\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let player_id = scene.add_player(self.config.player_start_position);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_player_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `delta_time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/game_templates.rs","byte_start":15308,"byte_end":15318,"line_start":542,"line_end":542,"column_start":26,"column_end":36,"is_primary":true,"text":[{"text":"    fn update(&mut self, delta_time: f32, input: &InputManager, framework: &mut GameFramework) -> GameUpdateResult {","highlight_start":26,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/game_templates.rs","byte_start":15308,"byte_end":15318,"line_start":542,"line_end":542,"column_start":26,"column_end":36,"is_primary":true,"text":[{"text":"    fn update(&mut self, delta_time: f32, input: &InputManager, framework: &mut GameFramework) -> GameUpdateResult {","highlight_start":26,"highlight_end":36}],"label":null,"suggested_replacement":"_delta_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `delta_time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_templates.rs:542:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m542\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mt self, delta_time: f32, input: &InputManager, framework: &mut GameFramework) -> GameUpdateResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_delta_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `framework`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/game_templates.rs","byte_start":15347,"byte_end":15356,"line_start":542,"line_end":542,"column_start":65,"column_end":74,"is_primary":true,"text":[{"text":"    fn update(&mut self, delta_time: f32, input: &InputManager, framework: &mut GameFramework) -> GameUpdateResult {","highlight_start":65,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/game_templates.rs","byte_start":15347,"byte_end":15356,"line_start":542,"line_end":542,"column_start":65,"column_end":74,"is_primary":true,"text":[{"text":"    fn update(&mut self, delta_time: f32, input: &InputManager, framework: &mut GameFramework) -> GameUpdateResult {","highlight_start":65,"highlight_end":74}],"label":null,"suggested_replacement":"_framework","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `framework`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_templates.rs:542:65\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m542\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mManager, framework: &mut GameFramework) -> GameUpdateResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_framework`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `size`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/game_templates.rs","byte_start":31966,"byte_end":31970,"line_start":1018,"line_end":1018,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"        for (position, size) in platforms {","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/game_templates.rs","byte_start":31966,"byte_end":31970,"line_start":1018,"line_end":1018,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"        for (position, size) in platforms {","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":"_size","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `size`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/game_templates.rs:1018:24\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1018\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        for (position, size) in platforms {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_size`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 3 previous errors; 34 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 3 previous errors; 34 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0412, E0502, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0412, E0502, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0412`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0412`.\u001b[0m\n"}
