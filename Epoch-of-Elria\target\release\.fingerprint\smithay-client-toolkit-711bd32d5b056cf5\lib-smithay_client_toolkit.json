{"rustc": 15597765236515928571, "features": "[\"andrew\", \"calloop\", \"default\", \"frames\"]", "declared_features": "[\"andrew\", \"calloop\", \"default\", \"frames\"]", "target": 7000747139203390044, "profile": 2040997289075261528, "path": 265126769871885649, "deps": [[3555588402607964811, "wayland_protocols", false, 4111132995149520487], [5986029879202738730, "log", false, 5597548263147149225], [6592966148407444940, "memmap2", false, 11864243790054602883], [6772218012066736839, "wayland_cursor", false, 9968962053673313790], [8109024259837965626, "andrew", false, 16432006588453375335], [8289600954469699483, "wayland_client", false, 16293535465224795781], [10435729446543529114, "bitflags", false, 5030592503563974220], [10847506952746196420, "nix", false, 2984705234678434717], [13936087210852657611, "calloop", false, 3114778371494109934], [17336073172670836519, "dlib", false, 17429494896215378274], [17917672826516349275, "lazy_static", false, 10975155677382447499]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/smithay-client-toolkit-711bd32d5b056cf5/dep-lib-smithay_client_toolkit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}