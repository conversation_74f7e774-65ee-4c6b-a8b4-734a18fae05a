@echo off
echo 🌍 Starting Epoch of Elria Game Engine...

REM Check if windowed engine exists, if not build it
if not exist "windowed_game_engine.exe" (
    echo 🔨 Building windowed game engine...
    g++ -std=c++17 -O2 windowed_game_engine.cpp -o windowed_game_engine.exe
    
    if %errorlevel% neq 0 (
        echo ❌ Build failed! Trying standalone version...
        if not exist "standalone_game_engine.exe" (
            g++ -std=c++17 -O2 standalone_game_engine.cpp -o standalone_game_engine.exe
        )
        
        if exist "standalone_game_engine.exe" (
            echo 🚀 Starting standalone engine...
            standalone_game_engine.exe
        ) else (
            echo ❌ Could not build engine. Trying existing games...
            if exist "simple_3d_game.exe" (
                simple_3d_game.exe
            ) else if exist "dream_weaver_complete.exe" (
                dream_weaver_complete.exe
            ) else (
                echo ❌ No games available. Please install MinGW-w64 or Visual Studio.
                pause
            )
        )
        exit /b 1
    )
)

echo 🚀 Launching windowed game engine...
windowed_game_engine.exe
