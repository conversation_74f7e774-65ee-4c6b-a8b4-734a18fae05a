/mnt/f/Epoch of Elria/target/debug/deps/libwayland_protocols-64d2dbf0d03f8f82.rmeta: /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/lib.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/protocol_macro.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/unstable.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/misc.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/wlr.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/stable.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/fullscreen-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/idle-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/input-method-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/input-timestamps-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/linux-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/linux-explicit-synchronization-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/pointer-constraints-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/pointer-gestures-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/primary-selection-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/relative-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/tablet-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/tablet-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/text-input-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/text-input-v3_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-decoration-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-foreign-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-foreign-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-output-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-shell-v5_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-shell-v6_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/gtk-primary-selection_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-data-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-export-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-gamma-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-input-inhibitor-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-layer-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-output-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-output-power-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-screencopy-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-virtual-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/presentation-time_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-shell_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/viewporter_client_api.rs

/mnt/f/Epoch of Elria/target/debug/deps/wayland_protocols-64d2dbf0d03f8f82.d: /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/lib.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/protocol_macro.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/unstable.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/misc.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/wlr.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/stable.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/fullscreen-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/idle-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/input-method-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/input-timestamps-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/linux-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/linux-explicit-synchronization-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/pointer-constraints-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/pointer-gestures-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/primary-selection-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/relative-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/tablet-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/tablet-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/text-input-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/text-input-v3_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-decoration-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-foreign-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-foreign-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-output-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-shell-v5_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-shell-v6_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/gtk-primary-selection_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-data-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-export-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-gamma-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-input-inhibitor-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-layer-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-output-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-output-power-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-screencopy-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-virtual-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/presentation-time_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-shell_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/viewporter_client_api.rs

/root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/lib.rs:
/root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/protocol_macro.rs:
/root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/unstable.rs:
/root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/misc.rs:
/root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/wlr.rs:
/root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/stable.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/fullscreen-shell-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/idle-inhibit-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/input-method-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/input-timestamps-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/keyboard-shortcuts-inhibit-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/linux-dmabuf-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/linux-explicit-synchronization-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/pointer-constraints-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/pointer-gestures-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/primary-selection-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/relative-pointer-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/tablet-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/tablet-v2_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/text-input-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/text-input-v3_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-decoration-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-foreign-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-foreign-v2_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-output-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-shell-v5_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-shell-v6_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xwayland-keyboard-grab-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/gtk-primary-selection_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-data-control-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-export-dmabuf-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-foreign-toplevel-management-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-gamma-control-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-input-inhibitor-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-layer-shell-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-output-management-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-output-power-management-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-screencopy-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/wlr-virtual-pointer-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/presentation-time_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/xdg-shell_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out/viewporter_client_api.rs:

# env-dep:OUT_DIR=/mnt/f/Epoch of Elria/target/debug/build/wayland-protocols-7bc6fa4d4e3674c5/out
