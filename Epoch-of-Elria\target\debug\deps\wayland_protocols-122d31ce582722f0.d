/mnt/f/Epoch of Elria/target/debug/deps/libwayland_protocols-122d31ce582722f0.rmeta: /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/lib.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/protocol_macro.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/unstable.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/misc.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/wlr.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/stable.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/fullscreen-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/idle-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/input-method-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/input-timestamps-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/linux-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/linux-explicit-synchronization-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/pointer-constraints-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/pointer-gestures-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/primary-selection-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/relative-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/tablet-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/tablet-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/text-input-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/text-input-v3_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-decoration-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-foreign-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-foreign-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-output-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-shell-v5_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-shell-v6_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/gtk-primary-selection_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/input-method-unstable-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/server-decoration_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-data-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-export-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-gamma-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-input-inhibitor-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-layer-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-output-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-output-power-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-screencopy-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-virtual-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/presentation-time_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-shell_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/viewporter_client_api.rs

/mnt/f/Epoch of Elria/target/debug/deps/libwayland_protocols-122d31ce582722f0.rlib: /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/lib.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/protocol_macro.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/unstable.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/misc.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/wlr.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/stable.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/fullscreen-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/idle-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/input-method-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/input-timestamps-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/linux-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/linux-explicit-synchronization-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/pointer-constraints-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/pointer-gestures-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/primary-selection-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/relative-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/tablet-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/tablet-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/text-input-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/text-input-v3_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-decoration-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-foreign-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-foreign-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-output-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-shell-v5_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-shell-v6_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/gtk-primary-selection_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/input-method-unstable-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/server-decoration_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-data-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-export-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-gamma-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-input-inhibitor-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-layer-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-output-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-output-power-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-screencopy-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-virtual-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/presentation-time_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-shell_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/viewporter_client_api.rs

/mnt/f/Epoch of Elria/target/debug/deps/wayland_protocols-122d31ce582722f0.d: /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/lib.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/protocol_macro.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/unstable.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/misc.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/wlr.rs /root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/stable.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/fullscreen-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/idle-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/input-method-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/input-timestamps-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/linux-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/linux-explicit-synchronization-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/pointer-constraints-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/pointer-gestures-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/primary-selection-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/relative-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/tablet-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/tablet-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/text-input-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/text-input-v3_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-decoration-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-foreign-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-foreign-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-output-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-shell-v5_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-shell-v6_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/gtk-primary-selection_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/input-method-unstable-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/server-decoration_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-data-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-export-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-gamma-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-input-inhibitor-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-layer-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-output-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-output-power-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-screencopy-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-virtual-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/presentation-time_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-shell_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/viewporter_client_api.rs

/root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/lib.rs:
/root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/protocol_macro.rs:
/root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/unstable.rs:
/root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/misc.rs:
/root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/wlr.rs:
/root/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/stable.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/fullscreen-shell-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/idle-inhibit-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/input-method-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/input-timestamps-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/keyboard-shortcuts-inhibit-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/linux-dmabuf-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/linux-explicit-synchronization-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/pointer-constraints-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/pointer-gestures-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/primary-selection-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/relative-pointer-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/tablet-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/tablet-v2_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/text-input-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/text-input-v3_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-decoration-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-foreign-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-foreign-v2_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-output-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-shell-v5_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-shell-v6_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xwayland-keyboard-grab-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/gtk-primary-selection_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/input-method-unstable-v2_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/server-decoration_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-data-control-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-export-dmabuf-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-foreign-toplevel-management-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-gamma-control-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-input-inhibitor-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-layer-shell-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-output-management-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-output-power-management-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-screencopy-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/wlr-virtual-pointer-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/presentation-time_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/xdg-shell_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out/viewporter_client_api.rs:

# env-dep:OUT_DIR=/mnt/f/Epoch of Elria/target/debug/build/wayland-protocols-c4a3dc1b316010b6/out
