{"rustc": 15597765236515928571, "features": "[\"mio\", \"mio-extras\", \"parking_lot\", \"percent-encoding\", \"sctk\", \"wayland\", \"wayland-client\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"mio\", \"mio-extras\", \"parking_lot\", \"percent-encoding\", \"sctk\", \"serde\", \"std_web\", \"stdweb\", \"wasm-bindgen\", \"wayland\", \"wayland-client\", \"web-sys\", \"web_sys\", \"x11\", \"x11-dl\"]", "target": 5956907280325987529, "profile": 2040997289075261528, "path": 3552221278819351893, "deps": [[40386456601120721, "percent_encoding", false, 193109855468323108], [2219490556472398059, "mio_extras", false, 2908635524960373764], [2481439302769428604, "raw_window_handle", false, 1760758205699193800], [2924422107542798392, "libc", false, 11562270898679172843], [5573101603161346839, "x11_dl", false, 9553316770780755343], [5986029879202738730, "log", false, 5597548263147149225], [8289600954469699483, "wayland_client", false, 16293535465224795781], [10435729446543529114, "bitflags", false, 5030592503563974220], [11641406201058336332, "parking_lot", false, 32145397734826825], [14196108479452351812, "instant", false, 12985320796132169642], [15503054242377401778, "sctk", false, 16363876191154831056], [16292302275207019187, "mio", false, 14880271563641489256], [17917672826516349275, "lazy_static", false, 10975155677382447499]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/winit-e20209f44b91c3e0/dep-lib-winit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}